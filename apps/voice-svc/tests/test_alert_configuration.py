"""
Tests for Prometheus alert configuration and latency monitoring.

This module tests the alert configuration in avr-alerts.yml and validates
that the metrics system can properly trigger alerts for high latency scenarios.
"""
import pytest
import yaml
import time
from pathlib import Path
from fastapi.testclient import TestClient
from services.metrics_service import get_metrics_service, reset_metrics_service
from main import app


class TestAlertConfiguration:
    """Test alert configuration and latency monitoring."""
    
    def setup_method(self):
        """Set up test fixtures."""
        reset_metrics_service()
        self.client = TestClient(app)
        self.metrics_service = get_metrics_service()
    
    def teardown_method(self):
        """Clean up after tests."""
        reset_metrics_service()
    
    def test_alert_file_exists_and_valid(self):
        """Test that avr-alerts.yml exists and contains valid YAML."""
        # Path to alert file (relative to project root)
        alert_file = Path(__file__).parent.parent.parent / "deploy" / "prometheus" / "avr-alerts.yml"
        
        assert alert_file.exists(), "avr-alerts.yml file should exist in project root"
        
        # Parse YAML to ensure it's valid
        with open(alert_file, 'r') as f:
            alert_config = yaml.safe_load(f)
        
        assert alert_config is not None, "Alert file should contain valid YAML"
        assert "groups" in alert_config, "Alert file should have 'groups' section"
        assert len(alert_config["groups"]) > 0, "Alert file should have at least one group"
    
    def test_p99_latency_alert_configuration(self):
        """Test that P99 latency alert is properly configured."""
        alert_file = Path(__file__).parent.parent.parent / "deploy" / "prometheus" / "avr-alerts.yml"
        
        with open(alert_file, 'r') as f:
            alert_config = yaml.safe_load(f)
        
        # Find the P99 latency alert
        p99_alert = None
        for group in alert_config["groups"]:
            for rule in group.get("rules", []):
                if rule.get("alert") == "P99LatencyHigh":
                    p99_alert = rule
                    break
        
        assert p99_alert is not None, "P99LatencyHigh alert should be defined"
        
        # Validate alert configuration
        assert "expr" in p99_alert, "Alert should have expression"
        assert "for" in p99_alert, "Alert should have duration"
        assert "labels" in p99_alert, "Alert should have labels"
        assert "annotations" in p99_alert, "Alert should have annotations"
        
        # Validate expression uses correct metric name
        expr = p99_alert["expr"]
        assert "avr_call_latency_seconds_bucket" in expr, "Alert should use correct metric name"
        assert "histogram_quantile(0.99" in expr, "Alert should calculate P99"
        assert "> 0.8" in expr, "Alert should trigger at 800ms threshold"
        
        # Validate timing
        assert p99_alert["for"] == "2m", "Alert should wait 2 minutes before firing"
        
        # Validate labels and annotations
        assert p99_alert["labels"]["severity"] == "warning", "Alert should be warning severity"
        assert "summary" in p99_alert["annotations"], "Alert should have summary"
        assert "description" in p99_alert["annotations"], "Alert should have description"
    
    def test_latency_metrics_bucket_generation(self):
        """Test that latency metrics generate proper histogram buckets."""
        tenant_id = "test-tenant-alert"
        
        # Record various latency values
        latencies = [0.05, 0.15, 0.3, 0.6, 0.9, 1.2, 2.5]  # Mix of low and high latencies
        
        for latency in latencies:
            self.metrics_service.record_call_latency(tenant_id, latency)
        
        # Get metrics data
        response = self.client.get("/metrics")
        assert response.status_code == 200
        content = response.text
        
        # Verify histogram buckets are present
        assert "avr_call_latency_seconds_bucket" in content
        assert f'tenant_id="{tenant_id}"' in content
        
        # Verify specific buckets exist (from our metric definition)
        expected_buckets = ["0.1", "0.2", "0.5", "0.8", "1.0", "2.0", "5.0", "10.0", "+Inf"]
        for bucket in expected_buckets:
            assert f'le="{bucket}"' in content, f"Bucket {bucket} should be present"
    
    def test_high_latency_scenario_above_threshold(self):
        """Test scenario where latency is consistently above 800ms threshold."""
        tenant_id = "high-latency-tenant"
        
        # Simulate high latency calls (above 800ms threshold)
        high_latencies = [0.85, 0.9, 0.95, 1.0, 1.1, 1.2, 1.5, 2.0]
        
        for latency in high_latencies:
            self.metrics_service.record_call_latency(tenant_id, latency)
        
        # Get metrics and verify data
        response = self.client.get("/metrics")
        assert response.status_code == 200
        content = response.text
        
        # Verify metrics are recorded
        assert f'tenant_id="{tenant_id}"' in content
        
        # Count samples in buckets that would affect P99 calculation
        # For P99 with 8 samples, we need to look at bucket distributions
        lines = content.split('\n')
        bucket_counts = {}
        
        for line in lines:
            if (f'tenant_id="{tenant_id}"' in line and 
                'avr_call_latency_seconds_bucket' in line and 
                not line.startswith('#')):
                
                # Extract bucket value and count
                if 'le="' in line:
                    le_start = line.find('le="') + 4
                    le_end = line.find('"', le_start)
                    bucket_le = line[le_start:le_end]
                    
                    count_start = line.rfind(' ') + 1
                    count = float(line[count_start:])
                    bucket_counts[bucket_le] = count
        
        # Verify that most samples are in higher buckets (above 0.8)
        assert bucket_counts.get("0.8", 0) < len(high_latencies), "Most samples should be above 0.8s"
        assert bucket_counts.get("+Inf", 0) == len(high_latencies), "All samples should be in +Inf bucket"
    
    def test_low_latency_scenario_below_threshold(self):
        """Test scenario where latency is consistently below 800ms threshold."""
        tenant_id = "low-latency-tenant"
        
        # Simulate low latency calls (below 800ms threshold)
        low_latencies = [0.1, 0.15, 0.2, 0.25, 0.3, 0.4, 0.5, 0.6]
        
        for latency in low_latencies:
            self.metrics_service.record_call_latency(tenant_id, latency)
        
        # Get metrics and verify data
        response = self.client.get("/metrics")
        assert response.status_code == 200
        content = response.text
        
        # Verify metrics are recorded
        assert f'tenant_id="{tenant_id}"' in content
        
        # Parse bucket counts
        lines = content.split('\n')
        bucket_counts = {}
        
        for line in lines:
            if (f'tenant_id="{tenant_id}"' in line and 
                'avr_call_latency_seconds_bucket' in line and 
                not line.startswith('#')):
                
                if 'le="' in line:
                    le_start = line.find('le="') + 4
                    le_end = line.find('"', le_start)
                    bucket_le = line[le_start:le_end]
                    
                    count_start = line.rfind(' ') + 1
                    count = float(line[count_start:])
                    bucket_counts[bucket_le] = count
        
        # Verify that all samples are below 0.8s threshold
        assert bucket_counts.get("0.8", 0) == len(low_latencies), "All samples should be below 0.8s"
        assert bucket_counts.get("+Inf", 0) == len(low_latencies), "All samples should be in +Inf bucket"
    
    def test_mixed_latency_scenario_p99_calculation(self):
        """Test mixed latency scenario to validate P99 calculation logic."""
        tenant_id = "mixed-latency-tenant"
        
        # Create a dataset where P99 would be above threshold
        # 100 samples: 98 low latency, 2 high latency
        # P99 should capture the high latency samples
        latencies = []
        
        # 98 samples below threshold (0.1-0.7s)
        for i in range(98):
            latencies.append(0.1 + (i % 6) * 0.1)  # 0.1, 0.2, 0.3, 0.4, 0.5, 0.6
        
        # 2 samples above threshold (1.0s and 1.5s)
        latencies.extend([1.0, 1.5])
        
        # Record all latencies
        for latency in latencies:
            self.metrics_service.record_call_latency(tenant_id, latency)
        
        # Get metrics
        response = self.client.get("/metrics")
        assert response.status_code == 200
        content = response.text
        
        # Verify metrics are recorded
        assert f'tenant_id="{tenant_id}"' in content
        assert "avr_call_latency_seconds_bucket" in content
        
        # Parse and verify bucket distribution
        lines = content.split('\n')
        bucket_counts = {}
        
        for line in lines:
            if (f'tenant_id="{tenant_id}"' in line and 
                'avr_call_latency_seconds_bucket' in line and 
                not line.startswith('#')):
                
                if 'le="' in line:
                    le_start = line.find('le="') + 4
                    le_end = line.find('"', le_start)
                    bucket_le = line[le_start:le_end]
                    
                    count_start = line.rfind(' ') + 1
                    count = float(line[count_start:])
                    bucket_counts[bucket_le] = count
        
        # Verify bucket distribution makes sense for P99 calculation
        assert bucket_counts.get("0.8", 0) == 98, "98 samples should be below 0.8s"
        assert bucket_counts.get("1.0", 0) == 99, "99 samples should be below 1.0s"
        assert bucket_counts.get("2.0", 0) == 100, "All 100 samples should be below 2.0s"
        assert bucket_counts.get("+Inf", 0) == 100, "All samples should be in +Inf bucket"
    
    def test_context_manager_latency_recording(self):
        """Test that context manager properly records latencies for alert monitoring."""
        tenant_id = "context-manager-tenant"
        
        # Use context manager to simulate high latency operation
        with self.metrics_service.time_call_latency(tenant_id):
            time.sleep(0.05)  # Simulate 50ms operation
        
        # Use context manager to simulate very high latency operation
        with self.metrics_service.time_call_latency(tenant_id):
            time.sleep(0.1)  # Simulate 100ms operation
        
        # Get metrics
        response = self.client.get("/metrics")
        assert response.status_code == 200
        content = response.text
        
        # Verify metrics are recorded
        assert f'tenant_id="{tenant_id}"' in content
        assert "avr_call_latency_seconds_bucket" in content
        
        # Verify we have histogram data
        lines = [line for line in content.split('\n') 
                if f'tenant_id="{tenant_id}"' in line and 'avr_call_latency_seconds' in line]
        
        assert len(lines) > 0, "Should have latency metrics recorded"
    
    def test_prometheus_query_syntax_validation(self):
        """Test that the alert query syntax is valid for Prometheus."""
        alert_file = Path(__file__).parent.parent.parent / "deploy" / "prometheus" / "avr-alerts.yml"
        
        with open(alert_file, 'r') as f:
            alert_config = yaml.safe_load(f)
        
        # Find P99 alert expression
        p99_expr = None
        for group in alert_config["groups"]:
            for rule in group.get("rules", []):
                if rule.get("alert") == "P99LatencyHigh":
                    p99_expr = rule["expr"]
                    break
        
        assert p99_expr is not None, "Should find P99 alert expression"
        
        # Validate Prometheus query syntax elements
        assert "histogram_quantile(" in p99_expr, "Should use histogram_quantile function"
        assert "0.99" in p99_expr, "Should calculate 99th percentile"
        assert "rate(" in p99_expr, "Should use rate function"
        assert "[5m]" in p99_expr, "Should use 5-minute time window"
        assert "avr_call_latency_seconds_bucket" in p99_expr, "Should reference correct metric"
        assert "> 0.8" in p99_expr, "Should compare against 0.8 threshold"
        
        # Validate the complete expression structure
        expected_pattern = "histogram_quantile(0.99, rate(avr_call_latency_seconds_bucket[5m])) > 0.8"
        assert p99_expr == expected_pattern, f"Expression should match expected pattern: {expected_pattern}"
