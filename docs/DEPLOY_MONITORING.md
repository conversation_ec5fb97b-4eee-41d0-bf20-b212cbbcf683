# Monitoring and Alerting Deployment Guide

This guide covers deploying the Prometheus monitoring stack with Alertmanager for the AiLex Voice Service.

## Overview

The monitoring stack includes:
- **Prometheus**: Metrics collection and alerting rules
- **Alertmanager**: Alert routing and notification delivery
- **Grafana**: Metrics visualization (optional)

## Alert Configuration

### Alert Rules

Alert rules are defined in `deploy/prometheus/avr-alerts.yml`:

- **P99LatencyHigh**: Triggers when voice round-trip latency P99 > 800ms for 2+ minutes
- Uses metric: `avr_call_latency_seconds_bucket`
- Severity: `warning`

### Alert Delivery

Alertmanager configuration is in `deploy/prometheus/alertmanager.yml`:

- **Default receiver**: Slack notifications to `#ailex-alerts`
- **Grouping**: Alerts grouped by `alertname`
- **Resolve timeout**: 5 minutes

## Deployment Steps

### 1. Set Slack Webhook URL

Configure the Slack webhook URL as a secret in your Fly app:

```bash
fly secrets set SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
  -a avr-voice-svc
```

**Note**: Replace `YOUR/SLACK/WEBHOOK` with your actual Slack webhook path.

### 2. Container Configuration

Ensure your Prometheus container mounts the configuration files:

```dockerfile
# In your Dockerfile or docker-compose.yml
COPY deploy/prometheus/avr-alerts.yml /etc/prometheus/avr-alerts.yml
COPY deploy/prometheus/alertmanager.yml /etc/alertmanager/alertmanager.yml
```

### 3. Environment Variable Substitution

The alertmanager configuration uses `__SLACK_WEBHOOK_URL__` placeholder that should be replaced at runtime:

```bash
# Example startup script
envsubst < /etc/alertmanager/alertmanager.yml.template > /etc/alertmanager/alertmanager.yml
```

### 4. Service Ports

Ensure the following ports are configured:

- **Prometheus**: `:9090`
- **Alertmanager**: `:9093`
- **Voice Service Metrics**: `:8001` (METRICS_PORT)

## Adding Additional Receivers

### Email Notifications

Add email receiver to `alertmanager.yml`:

```yaml
receivers:
  - name: 'slack_main'
    slack_configs:
      - api_url: '__SLACK_WEBHOOK_URL__'
        channel: '#ailex-alerts'
        title: '{{ .CommonLabels.alertname }}'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}\n{{ end }}'
    email_configs:
      - to: '<EMAIL>'
        from: '<EMAIL>'
        smarthost: 'smtp.yourcompany.com:587'
        subject: 'AiLex Alert: {{ .CommonLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          {{ end }}
```

### PagerDuty Integration

Add PagerDuty receiver:

```yaml
receivers:
  - name: 'pagerduty_critical'
    pagerduty_configs:
      - routing_key: '__PAGERDUTY_INTEGRATION_KEY__'
        description: 'AiLex Voice Service Alert: {{ .CommonLabels.alertname }}'
```

Then set the PagerDuty integration key:

```bash
fly secrets set PAGERDUTY_INTEGRATION_KEY="your-pagerduty-key" -a avr-voice-svc
```

### Multiple Receivers with Routing

Configure different receivers based on severity:

```yaml
route:
  group_by: ['alertname']
  receiver: 'slack_main'
  routes:
    - match:
        severity: critical
      receiver: 'pagerduty_critical'
    - match:
        severity: warning
      receiver: 'slack_main'

receivers:
  - name: 'slack_main'
    # ... slack config
  - name: 'pagerduty_critical'
    # ... pagerduty config
```

## Testing Alerts

### Manual Alert Testing

1. **Generate high latency**: Use load testing to create latency above 800ms
2. **Check Prometheus**: Verify metrics appear at `/metrics` endpoint
3. **Verify alert firing**: Check Prometheus alerts page at `:9090/alerts`
4. **Confirm delivery**: Check Slack channel for notifications

### Alert Validation

```bash
# Validate alertmanager config
amtool check-config /etc/alertmanager/alertmanager.yml

# Test alert routing
amtool config routes test --config.file=/etc/alertmanager/alertmanager.yml
```

## Monitoring Health

### Key Metrics to Monitor

- `avr_call_latency_seconds_bucket`: Voice round-trip latency
- `avr_active_calls`: Current active calls per tenant
- `avr_queue_depth`: Call queue depth per tenant
- `avr_calls_rejected_total`: Rejected calls counter

### Grafana Dashboard

Import the dashboard from `grafana/avr_dashboard.json` for visualization.

## Troubleshooting

### Common Issues

1. **Alerts not firing**:
   - Check metric names in alert rules match actual metrics
   - Verify Prometheus can scrape voice service metrics
   - Check alert rule syntax with `promtool check rules`

2. **Notifications not delivered**:
   - Verify Slack webhook URL is correct
   - Check Alertmanager logs for delivery errors
   - Test webhook URL manually with curl

3. **Environment variable substitution**:
   - Ensure `envsubst` is available in container
   - Verify secret is properly set in Fly app
   - Check container startup logs

### Debugging Commands

```bash
# Check Prometheus targets
curl http://localhost:9090/api/v1/targets

# Check active alerts
curl http://localhost:9090/api/v1/alerts

# Check Alertmanager status
curl http://localhost:9093/api/v1/status

# Test Slack webhook
curl -X POST -H 'Content-type: application/json' \
  --data '{"text":"Test alert from AiLex monitoring"}' \
  YOUR_SLACK_WEBHOOK_URL
```

## Security Considerations

- Store webhook URLs and API keys as secrets, not in configuration files
- Use HTTPS for all webhook endpoints
- Regularly rotate API keys and webhook URLs
- Limit network access to monitoring ports (9090, 9093)

## Maintenance

- **Regular updates**: Keep Prometheus and Alertmanager versions current
- **Alert tuning**: Adjust thresholds based on production metrics
- **Retention**: Configure appropriate data retention policies
- **Backup**: Backup alert rules and configuration files
