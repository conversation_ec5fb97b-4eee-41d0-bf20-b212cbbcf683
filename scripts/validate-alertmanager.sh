#!/bin/bash

# Validate Alertmanager configuration
# This script checks that alertmanager.yml is valid YAML and can be parsed by amtool

set -e

echo "🔍 Validating Alertmanager configuration..."

# Check if alertmanager config exists
ALERTMANAGER_CONFIG="deploy/prometheus/alertmanager.yml"
if [ ! -f "$ALERTMANAGER_CONFIG" ]; then
    echo "❌ Error: $ALERTMANAGER_CONFIG not found"
    exit 1
fi

echo "✅ Found alertmanager configuration at $ALERTMANAGER_CONFIG"

# Check if it's valid YAML
echo "🔍 Checking YAML syntax..."
if command -v yq >/dev/null 2>&1; then
    yq eval '.' "$ALERTMANAGER_CONFIG" >/dev/null
    echo "✅ YAML syntax is valid"
else
    echo "⚠️  Warning: yq not found, skipping YAML syntax check"
fi

# Check if amtool is available for config validation
if command -v amtool >/dev/null 2>&1; then
    echo "🔍 Validating with amtool..."
    
    # Create a temporary config with placeholder substituted for validation
    TEMP_CONFIG=$(mktemp)
    sed 's/__SLACK_WEBHOOK_URL__/https:\/\/hooks.slack.com\/services\/TEST\/TEST\/TEST/' "$ALERTMANAGER_CONFIG" > "$TEMP_CONFIG"
    
    # Validate the configuration
    if amtool check-config "$TEMP_CONFIG"; then
        echo "✅ Alertmanager configuration is valid"
    else
        echo "❌ Alertmanager configuration validation failed"
        rm -f "$TEMP_CONFIG"
        exit 1
    fi
    
    rm -f "$TEMP_CONFIG"
else
    echo "⚠️  Warning: amtool not found, skipping detailed validation"
    echo "   Install alertmanager to enable full validation"
fi

# Check for required fields
echo "🔍 Checking required configuration fields..."

# Check for global section
if ! grep -q "^global:" "$ALERTMANAGER_CONFIG"; then
    echo "❌ Error: Missing 'global' section"
    exit 1
fi

# Check for route section
if ! grep -q "^route:" "$ALERTMANAGER_CONFIG"; then
    echo "❌ Error: Missing 'route' section"
    exit 1
fi

# Check for receivers section
if ! grep -q "^receivers:" "$ALERTMANAGER_CONFIG"; then
    echo "❌ Error: Missing 'receivers' section"
    exit 1
fi

# Check for placeholder substitution
if ! grep -q "__SLACK_WEBHOOK_URL__" "$ALERTMANAGER_CONFIG"; then
    echo "❌ Error: Missing __SLACK_WEBHOOK_URL__ placeholder"
    exit 1
fi

echo "✅ All required fields present"

# Validate alert rules file
ALERT_RULES="deploy/prometheus/avr-alerts.yml"
if [ -f "$ALERT_RULES" ]; then
    echo "🔍 Validating alert rules..."
    
    if command -v promtool >/dev/null 2>&1; then
        if promtool check rules "$ALERT_RULES"; then
            echo "✅ Alert rules are valid"
        else
            echo "❌ Alert rules validation failed"
            exit 1
        fi
    else
        echo "⚠️  Warning: promtool not found, skipping alert rules validation"
        echo "   Install prometheus to enable alert rules validation"
    fi
else
    echo "⚠️  Warning: Alert rules file not found at $ALERT_RULES"
fi

echo "🎉 Alertmanager configuration validation completed successfully!"
