# Prometheus configuration for AiLex Voice Service monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/avr-alerts.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['localhost:9093']

scrape_configs:
  # Voice Service metrics
  - job_name: 'voice-svc'
    static_configs:
      - targets: ['voice-svc:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s
    scrape_timeout: 4s

  # Calendar Service metrics (if available)
  - job_name: 'calendar-svc'
    static_configs:
      - targets: ['calendar-svc:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
