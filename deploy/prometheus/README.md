# Prometheus Monitoring Configuration

This directory contains the Prometheus monitoring stack configuration for AiLex Voice Service.

## Files

### `avr-alerts.yml`
Prometheus alert rules for voice service monitoring:
- **P99LatencyHigh**: Triggers when voice round-trip latency P99 > 800ms for 2+ minutes
- Uses metric: `avr_call_latency_seconds_bucket`
- Severity: `warning`

### `alertmanager.yml`
Alertmanager configuration for alert routing and delivery:
- **Default receiver**: Slack notifications to `#ailex-alerts`
- **Grouping**: Alerts grouped by `alertname`
- **Resolve timeout**: 5 minutes
- **Environment substitution**: Uses `__SLACK_WEBHOOK_URL__` placeholder

### `docker-compose.monitoring.yml`
Example Docker Compose configuration showing how to:
- Mount configuration files into containers
- Set up environment variable substitution
- Configure Prometheus, Alertmanager, and Grafana services

## Quick Start

1. **Set Slack webhook URL**:
   ```bash
   fly secrets set SLACK_WEBHOOK_URL="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" -a avr-voice-svc
   ```

2. **Mount configurations in your container**:
   ```dockerfile
   COPY deploy/prometheus/avr-alerts.yml /etc/prometheus/avr-alerts.yml
   COPY deploy/prometheus/alertmanager.yml /etc/alertmanager/alertmanager.yml
   ```

3. **Configure environment substitution**:
   ```bash
   envsubst < /etc/alertmanager/alertmanager.yml.template > /etc/alertmanager/alertmanager.yml
   ```

## Validation

Run the validation script to check configuration:
```bash
./scripts/validate-alertmanager.sh
```

## Documentation

See [docs/DEPLOY_MONITORING.md](../../docs/DEPLOY_MONITORING.md) for complete deployment guide.

## Testing

Alert configuration tests are in `apps/voice-svc/tests/test_alert_configuration.py`.
